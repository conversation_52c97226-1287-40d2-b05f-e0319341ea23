<?php

namespace Tests\Unit\Models;

use App\Models\Domain;
use App\Models\Link;
use App\Models\LinkVisit;
use App\Observers\LinkVisitObserver;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LinkVisitObserverTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }

    #[Test]
    public function it_increments_link_visit_count_when_visit_is_created(): void
    {
        // Create a link with initial visit count of 0
        $link = Link::factory()->create(['visit_count' => 0]);

        // Create a visit for the link
        $visit = new LinkVisit([
            'link_id' => $link->id,
            'domain_id' => Domain::first()->id,
            'ip' => '127.0.0.1',
        ]);
        $visit->save();

        // Refresh the link from the database
        $link->refresh();

        // Check that the visit count was incremented
        $this->assertEquals(1, $link->visit_count);
    }

    #[Test]
    public function it_increments_link_visit_count_multiple_times(): void
    {
        // Create a link with initial visit count of 0
        $link = Link::factory()->create(['visit_count' => 0]);

        // Create multiple visits for the link
        for ($i = 0; $i < 3; $i++) {
            $visit = new LinkVisit([
                'link_id' => $link->id,
                'domain_id' => Domain::first()->id,
                'ip' => '127.0.0.1',
            ]);
            $visit->save();
        }

        // Refresh the link from the database
        $link->refresh();

        // Check that the visit count was incremented for each visit
        $this->assertEquals(3, $link->visit_count);
    }

    #[Test]
    public function it_manually_calls_observer_methods(): void
    {
        // Create a link with initial visit count of 0
        $link = Link::factory()->create(['visit_count' => 0]);

        // Create a visit for the link
        $visit = new LinkVisit([
            'link_id' => $link->id,
            'domain_id' => Domain::first()->id,
            'ip' => '127.0.0.1',
        ]);

        // Manually call the observer method
        $observer = new LinkVisitObserver;
        $observer->created($visit);

        // Refresh the link from the database
        $link->refresh();

        // Check that the visit count was incremented
        $this->assertEquals(1, $link->visit_count);
    }
}

name: <PERSON>uild and Push Docker Images

on:
  push:
    branches: [main, master, octane2]

env:
  REGISTRY: registry.gitlab.com
  PROJECT: ${{ secrets.GITLAB_PROJECT_PATH }}

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - dockerfile: Web.Dockerfile
            image: web
          - dockerfile: QueueWorker.Dockerfile
            image: queue

    steps:
      - uses: actions/checkout@v4

      - name: Login to GitLab Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ secrets.GITLAB_USERNAME }}
          password: ${{ secrets.GITLAB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ${{ matrix.dockerfile }}
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.PROJECT }}/${{ matrix.image }}:latest



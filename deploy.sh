#!/bin/bash

# Production Deployment Script for Tiny URL
set -e

echo "🚀 Starting production deployment..."

# Check if .env.prod exists
if [ ! -f ".env.prod" ]; then
    echo "❌ .env.prod file not found!"
    echo "📝 Please copy .env.prod.example to .env.prod and configure it"
    exit 1
fi

# Load environment variables
export $(cat .env.prod | grep -v '^#' | xargs)

# Validate required variables
if [ -z "$GITLAB_PROJECT_PATH" ]; then
    echo "❌ GITLAB_PROJECT_PATH not set in .env.prod"
    exit 1
fi

echo "📦 Pulling latest images from GitLab Container Registry..."
docker pull registry.gitlab.com/sbamtr-my-projects/tiny_url/web:latest
docker pull registry.gitlab.com/sbamtr-my-projects/tiny_url/queue:latest

echo "🔄 Stopping existing containers..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod down

echo "🏗️ Starting production services..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d

echo "⏳ Waiting for services to be healthy..."
sleep 10

echo "🔍 Checking service status..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod ps

echo "📊 Running database migrations..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan migrate --force

echo "🧹 Clearing application cache..."
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan config:cache
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan route:cache
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan view:cache

echo "✅ Deployment completed successfully!"
echo "🌐 Application should be available at: ${APP_URL:-http://localhost:8000}"
echo "📋 Check logs with: docker-compose -f docker-compose.prod.yml logs -f"

# Production Deployment Guide

This guide explains how to deploy the Tiny URL application using the Docker images built by GitHub Actions and pushed to GitLab Container Registry.

## 📋 Prerequisites

- Docker and Docker Compose installed
- Access to GitLab Container Registry
- Domain name configured (optional)

## 🚀 Quick Start

### 1. Clone Repository (Production Server)

```bash
git clone https://github.com/your-username/tiny-url.git
cd tiny-url
```

### 2. Configure Environment

```bash
# Copy the example environment file
cp .env.prod.example .env.prod

# Edit the configuration
nano .env.prod
```

**Required Configuration:**

```env
# GitLab Container Registry
********************your-username/tiny-url

# Application
APP_KEY=base64:your-generated-app-key-here
APP_URL=https://your-domain.com

# Database
DB_DATABASE=tiny_url_prod
DB_USERNAME=tiny_url_user
DB_PASSWORD=your-secure-password-here
```

### 3. Generate Application Key

```bash
# Generate a new Laravel application key
docker run --rm registry.gitlab.com/your-username/tiny-url/web:latest php artisan key:generate --show
```

Copy the generated key to your `.env.prod` file.

### 4. Deploy

```bash
# Make deploy script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh
```

## 📁 Files Overview

| File | Purpose |
|------|---------|
| `docker-compose.prod.yml` | Production Docker Compose configuration |
| `.env.prod.example` | Environment variables template |
| `deploy.sh` | Automated deployment script |

## 🏗️ Architecture

The production setup includes:

- **Web App Container**: FrankenPHP-based Laravel application
- **Queue Worker Container**: Background job processing (2 replicas)
- **PostgreSQL Database**: Persistent data storage
- **Redis Cache**: Session, cache, and queue storage

## 🔧 Manual Deployment Steps

If you prefer manual deployment:

### 1. Pull Images

```bash
docker pull registry.gitlab.com/your-username/tiny-url/web:latest
docker pull registry.gitlab.com/your-username/tiny-url/queue:latest
```

### 2. Start Services

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### 3. Run Migrations

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan migrate --force
```

### 4. Cache Configuration

```bash
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan config:cache
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan route:cache
docker-compose -f docker-compose.prod.yml --env-file .env.prod exec app php artisan view:cache
```

## 🔍 Monitoring

### Check Service Status

```bash
docker-compose -f docker-compose.prod.yml ps
```

### View Logs

```bash
# All services
docker-compose -f docker-compose.prod.yml logs -f

# Specific service
docker-compose -f docker-compose.prod.yml logs -f app
docker-compose -f docker-compose.prod.yml logs -f queue-worker
```

### Health Checks

The application includes built-in health checks:
- **Web App**: `http://your-domain.com/up`
- **Database**: Automatic PostgreSQL health check
- **Redis**: Automatic Redis health check

## 🔄 Updates

To deploy new versions:

```bash
# Pull latest images and restart
./deploy.sh
```

Or manually:

```bash
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d
```

## 🛡️ Security Considerations

1. **Environment Variables**: Keep `.env.prod` secure and never commit it
2. **Database Password**: Use a strong, unique password
3. **APP_KEY**: Generate a unique application key
4. **Firewall**: Only expose necessary ports (80, 443)
5. **SSL/TLS**: Use HTTPS in production (configure reverse proxy)

## 🌐 Reverse Proxy Setup (Nginx)

Example Nginx configuration:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🆘 Troubleshooting

### Common Issues

1. **Container won't start**
   - Check logs: `docker-compose -f docker-compose.prod.yml logs app`
   - Verify environment variables in `.env.prod`

2. **Database connection failed**
   - Ensure PostgreSQL container is healthy
   - Check database credentials

3. **Permission denied**
   - Verify file permissions: `chmod +x deploy.sh`

4. **Image pull failed**
   - Check GitLab Container Registry access
   - Verify `GITLAB_PROJECT_PATH` is correct

### Getting Help

- Check container logs for detailed error messages
- Verify all environment variables are set correctly
- Ensure Docker and Docker Compose are up to date
